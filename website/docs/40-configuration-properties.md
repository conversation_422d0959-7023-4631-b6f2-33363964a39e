---
sidebar_position: 40
---

# Configuration Properties

Configuration properties for the gRPC starter project.

This page was generated by [spring-configuration-property-documenter](https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc).

## Table of Contents
* [**grpc-client-boot-autoconfigure**](#grpc-client-boot-autoconfigure)
  * [**grpc.client.tls.key-manager** - `grpcstarter.client.GrpcClientProperties$Tls$KeyManager`](#grpc.client.tls.key-manager)

  * [**grpc.client.tls.trust-manager** - `grpcstarter.client.GrpcClientProperties$Tls$TrustManager`](#grpc.client.tls.trust-manager)

  * [**grpc.client** - `grpcstarter.client.GrpcClientProperties`](#grpc.client)

  * [**grpc.client.in-process** - `grpcstarter.client.GrpcClientProperties$InProcess`](#grpc.client.in-process)

  * [**grpc.client.refresh** - `grpcstarter.client.GrpcClientProperties$Refresh`](#grpc.client.refresh)

  * [**grpc.client.retry** - `grpcstarter.client.GrpcClientProperties$Retry`](#grpc.client.retry)

  * [**grpc.client.tls** - `grpcstarter.client.GrpcClientProperties$Tls`](#grpc.client.tls)
* [**grpc-server-boot-autoconfigure**](#grpc-server-boot-autoconfigure)
  * [**grpc.server.tls.key-manager** - `grpcstarter.server.GrpcServerProperties$Tls$KeyManager`](#grpc.server.tls.key-manager)

  * [**grpc.server.tls.trust-manager** - `grpcstarter.server.GrpcServerProperties$Tls$TrustManager`](#grpc.server.tls.trust-manager)

  * [**grpc.server.health.datasource** - `grpcstarter.server.GrpcServerProperties$Health$DataSource`](#grpc.server.health.datasource)

  * [**grpc.server.health.redis** - `grpcstarter.server.GrpcServerProperties$Health$Redis`](#grpc.server.health.redis)

  * [**grpc.server** - `grpcstarter.server.GrpcServerProperties`](#grpc.server)

  * [**grpc.server.channelz** - `grpcstarter.server.GrpcServerProperties$Channelz`](#grpc.server.channelz)

  * [**grpc.server.exception-handling** - `grpcstarter.server.GrpcServerProperties$ExceptionHandling`](#grpc.server.exception-handling)

  * [**grpc.server.health** - `grpcstarter.server.GrpcServerProperties$Health`](#grpc.server.health)

  * [**grpc.server.in-process** - `grpcstarter.server.GrpcServerProperties$InProcess`](#grpc.server.in-process)

  * [**grpc.server.reflection** - `grpcstarter.server.GrpcServerProperties$Reflection`](#grpc.server.reflection)

  * [**grpc.server.response** - `grpcstarter.server.GrpcServerProperties$Response`](#grpc.server.response)

  * [**grpc.server.tls** - `grpcstarter.server.GrpcServerProperties$Tls`](#grpc.server.tls)
* [**grpc-metrics**](#grpc-metrics)
  * [**grpc.metrics** - `grpcstarter.extensions.metrics.GrpcMetricsProperties`](#grpc.metrics)

  * [**grpc.metrics.client** - `grpcstarter.extensions.metrics.GrpcMetricsProperties$Client`](#grpc.metrics.client)

  * [**grpc.metrics.server** - `grpcstarter.extensions.metrics.GrpcMetricsProperties$Server`](#grpc.metrics.server)
* [**grpc-test**](#grpc-test)
  * [**grpc.test** - `grpcstarter.extensions.test.GrpcTestProperties`](#grpc.test)

  * [**grpc.test.server** - `grpcstarter.extensions.test.GrpcTestProperties$Server`](#grpc.test.server)
* [**grpc-tracing**](#grpc-tracing)
  * [**grpc.tracing** - `grpcstarter.extensions.tracing.GrpcTracingProperties`](#grpc.tracing)

  * [**grpc.tracing.client** - `grpcstarter.extensions.tracing.GrpcTracingProperties$Client`](#grpc.tracing.client)

  * [**grpc.tracing.server** - `grpcstarter.extensions.tracing.GrpcTracingProperties$Server`](#grpc.tracing.server)
* [**grpc-transcoding**](#grpc-transcoding)
  * [**grpc.transcoding** - `grpcstarter.extensions.transcoding.GrpcTranscodingProperties`](#grpc.transcoding)

  * [**grpc.transcoding.print-options** - `grpcstarter.extensions.transcoding.GrpcTranscodingProperties$PrintOptions`](#grpc.transcoding.print-options)
* [**grpc-validation**](#grpc-validation)
  * [**grpc.validation** - `grpcstarter.extensions.validation.GrpcValidationProperties`](#grpc.validation)

  * [**grpc.validation.client** - `grpcstarter.extensions.validation.GrpcValidationProperties$Client`](#grpc.validation.client)

  * [**grpc.validation.server** - `grpcstarter.extensions.validation.GrpcValidationProperties$Server`](#grpc.validation.server)

## grpc-client-boot-autoconfigure
### grpc.client.tls.key-manager
**Class:** `grpcstarter.client.GrpcClientProperties$Tls$KeyManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| cert-chain| org.springframework.core.io.Resource| | | | 
| private-key| org.springframework.core.io.Resource| | | | 
| private-key-password| java.lang.String| | | | 
### grpc.client.tls.trust-manager
**Class:** `grpcstarter.client.GrpcClientProperties$Tls$TrustManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| root-certs| org.springframework.core.io.Resource| | | | 
### grpc.client
**Class:** `grpcstarter.client.GrpcClientProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| authority| java.lang.String| | | | 
| base-packages| java.util.List&lt;java.lang.String&gt;| | | | 
| bean-definition-handler| java.lang.Class&lt;? extends grpcstarter.client.GrpcClientBeanDefinitionHandler&gt;| | | | 
| channels| java.util.List&lt;grpcstarter.client.GrpcClientProperties$Channel&gt;| | | | 
| clients| java.util.List&lt;java.lang.Class&lt;? extends io.grpc.stub.AbstractStub&gt;&gt;| | | | 
| compression| java.lang.String| | | | 
| deadline| java.lang.Long| | | | 
| enabled| java.lang.Boolean| | | | 
| max-inbound-message-size| org.springframework.util.unit.DataSize| | | | 
| max-inbound-metadata-size| org.springframework.util.unit.DataSize| | | | 
| max-outbound-message-size| org.springframework.util.unit.DataSize| | | | 
| metadata| java.util.List&lt;grpcstarter.client.GrpcClientProperties$Metadata&gt;| | | | 
| shutdown-timeout| java.lang.Long| | | | 
| ssl-bundle| java.lang.String| | | | 
### grpc.client.in-process
**Class:** `grpcstarter.client.GrpcClientProperties$InProcess`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| name| java.lang.String| | | | 
### grpc.client.refresh
**Class:** `grpcstarter.client.GrpcClientProperties$Refresh`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
### grpc.client.retry
**Class:** `grpcstarter.client.GrpcClientProperties$Retry`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| | | | 
| max-retry-attempts| java.lang.Integer| | | | 
| per-rpc-buffer-limit| org.springframework.util.unit.DataSize| | | | 
| retry-buffer-size| org.springframework.util.unit.DataSize| | | | 
### grpc.client.tls
**Class:** `grpcstarter.client.GrpcClientProperties$Tls`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|

## grpc-server-boot-autoconfigure
### grpc.server.tls.key-manager
**Class:** `grpcstarter.server.GrpcServerProperties$Tls$KeyManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| cert-chain| org.springframework.core.io.Resource| @see TlsServerCredentials.Builder#getCertificateChain()| | | 
| private-key| org.springframework.core.io.Resource| @see TlsServerCredentials.Builder#getPrivateKey()| | | 
| private-key-password| java.lang.String| @see TlsServerCredentials.Builder#getPrivateKeyPassword()| | | 
### grpc.server.tls.trust-manager
**Class:** `grpcstarter.server.GrpcServerProperties$Tls$TrustManager`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| root-certs| org.springframework.core.io.Resource| @see TlsServerCredentials.Builder#getRootCertificates()| | | 
### grpc.server.health.datasource
**Class:** `grpcstarter.server.GrpcServerProperties$Health$DataSource`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable datasource health check, default true.| true| | 
| service| java.lang.String| The service name that will be used for datasource health check, default value is &#x27;datasource&#x27;.| datasource| | 
| timeout| java.lang.Integer| The timeout in seconds for \{@link java.sql.Connection#isValid(int)}, use 0 if not set.| | | 
### grpc.server.health.redis
**Class:** `grpcstarter.server.GrpcServerProperties$Health$Redis`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable redis health check, default true.| true| | 
| service| java.lang.String| The service name that will be used for redis health check, default value is &#x27;redis&#x27;.| redis| | 
### grpc.server
**Class:** `grpcstarter.server.GrpcServerProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enable-empty-server| java.lang.Boolean| Whether to start a gRPC server when no service found, default true.| true| | 
| enabled| java.lang.Boolean| Whether to enable gRPC server autoconfiguration, default true.| true| | 
| max-inbound-message-size| org.springframework.util.unit.DataSize| The maximum message size allowed to be received on the server, default 4MB. @see GrpcUtil#DEFAULT_MAX_MESSAGE_SIZE| | | 
| max-inbound-metadata-size| org.springframework.util.unit.DataSize| The maximum size of metadata allowed to be received, default 8KB. @see GrpcUtil#DEFAULT_MAX_HEADER_LIST_SIZE| | | 
| port| java.lang.Integer| gRPC server port, default 9090, 0 or negative numbers will use random port.| 9090| | 
| shutdown-timeout| java.lang.Long| Graceful shutdown timeout, default 30s, if 0 will wait forever util all active calls finished.| 30000| | 
| ssl-bundle| java.lang.String| SSL bundle name for TLS configuration. &lt;p&gt; This is the preferred way to configure SSL/TLS for gRPC server. When specified, it takes precedence over the deprecated \{@link #tls} configuration. &lt;/p&gt; @since 3.5.3| | | 
### grpc.server.channelz
**Class:** `grpcstarter.server.GrpcServerProperties$Channelz`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to register \{@link ChannelzService}, default false.| false| | 
| max-page-size| java.lang.Integer| The maximum page size to return, default 100. @see AdminInterface| 100| | 
### grpc.server.exception-handling
**Class:** `grpcstarter.server.GrpcServerProperties$ExceptionHandling`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| default-exception-advice-enabled| java.lang.Boolean| Whether to enable \{@link DefaultGrpcExceptionAdvice}, default true. &lt;p&gt; \{@link DefaultGrpcExceptionAdvice} will handle exceptions recognized by gRPC, including: &lt;/p&gt; &lt;ul&gt; &lt;li&gt;\{@link StatusRuntimeException}&lt;/li&gt; &lt;li&gt;\{@link StatusException}&lt;/li&gt; &lt;/ul&gt; &lt;p&gt; When enabled, you can directly throw \{@link StatusRuntimeException} or \{@link StatusException} in service implementation, and the exception will be handled by \{@link DefaultGrpcExceptionAdvice}. &lt;/p&gt; &lt;pre&gt;\{@code @GrpcService public class SimpleService extends SimpleServiceGrpc.SimpleServiceImplBase \{ @Override public void unaryRpc(SimpleRequest request, StreamObserver&lt;SimpleResponse&gt; responseObserver) \{ throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription(&quot;Invalid request&quot;)); } } }&lt;/pre&gt; @see DefaultGrpcExceptionAdvice @since 3.2.3| true| | 
| enabled| java.lang.Boolean| Whether to enable exception handling, default true.| true| | 
### grpc.server.health
**Class:** `grpcstarter.server.GrpcServerProperties$Health`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable health check, default false.| true| | 
### grpc.server.in-process
**Class:** `grpcstarter.server.GrpcServerProperties$InProcess`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| name| java.lang.String| In-process server name, if configured, will create an in-process server, usually for testing.| | | 
### grpc.server.reflection
**Class:** `grpcstarter.server.GrpcServerProperties$Reflection`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to register reflection service, default false.| false| | 
### grpc.server.response
**Class:** `grpcstarter.server.GrpcServerProperties$Response`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| max-description-length| java.lang.Integer| The maximum length of response description. &lt;p&gt; When the length of the description exceeds this value, it will be truncated. &lt;/p&gt; @since 3.2.3| 2048| | 
### grpc.server.tls
**Class:** `grpcstarter.server.GrpcServerProperties$Tls`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|

## grpc-metrics
### grpc.metrics
**Class:** `grpcstarter.extensions.metrics.GrpcMetricsProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable metrics, default is \{@code true}| true| | 
### grpc.metrics.client
**Class:** `grpcstarter.extensions.metrics.GrpcMetricsProperties$Client`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable client metrics, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the client metrics interceptor. Default is \{@code 0}.| 0| | 
### grpc.metrics.server
**Class:** `grpcstarter.extensions.metrics.GrpcMetricsProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable server metrics, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the server metrics interceptor. Default is \{@code 0}.| 0| | 

## grpc-test
### grpc.test
**Class:** `grpcstarter.extensions.test.GrpcTestProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable test, default is \{@code true}.| true| | 
### grpc.test.server
**Class:** `grpcstarter.extensions.test.GrpcTestProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable test, default is \{@code true}.| true| | 
| port-type| grpcstarter.extensions.test.GrpcTestProperties$PortType| Port configuration, default is \{@link PortType#IN_PROCESS}, which means start grpc server with in-process transport. @see PortType| in-process| | 

## grpc-tracing
### grpc.tracing
**Class:** `grpcstarter.extensions.tracing.GrpcTracingProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable tracing, default is \{@code true}| true| | 
### grpc.tracing.client
**Class:** `grpcstarter.extensions.tracing.GrpcTracingProperties$Client`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable client tracing, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the client tracing interceptor. Default is \{@code 0}.| 0| | 
### grpc.tracing.server
**Class:** `grpcstarter.extensions.tracing.GrpcTracingProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| whether to enable server tracing, default is \{@code true}| true| | 
| order| java.lang.Integer| The order of the server tracing interceptor. Default is \{@code 0}.| 0| | 

## grpc-transcoding
### grpc.transcoding
**Class:** `grpcstarter.extensions.transcoding.GrpcTranscodingProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| auto-mapping| java.lang.Boolean| Whether to route methods without the &#x60;google.api.http&#x60; option, default true. &lt;p&gt; Example: &lt;pre&gt;\{@code package bookstore; service Bookstore \{ rpc GetShelf(GetShelfRequest) returns (Shelf) \{} } message GetShelfRequest \{ int64 shelf &#x3D; 1; } message Shelf \{} }&lt;/pre&gt; &lt;p&gt; The client could &#x60;post&#x60; a json body &#x60;\{&quot;shelf&quot;: 1234}&#x60; with the path of &#x60;/bookstore.Bookstore/GetShelfRequest&#x60; to call &#x60;GetShelfRequest&#x60;.| true| | 
| enabled| java.lang.Boolean| Whether to enable transcoding autoconfiguration, default \{@code true}.| true| | 
| endpoint| java.lang.String| gRPC server endpoint, if not set, will use \{@code localhost:$\{grpc.server.port}}. &lt;p&gt; In most cases, do not need to set this property explicitly.| | | 
### grpc.transcoding.print-options
**Class:** `grpcstarter.extensions.transcoding.GrpcTranscodingProperties$PrintOptions`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| add-whitespace| java.lang.Boolean| Whether to add spaces, line breaks and indentation to make the JSON output easy to read. Defaults to false.| false| | 
| always-print-enums-as-ints| java.lang.Boolean| Whether to always print enums as ints. By default they are rendered as strings. Defaults to false.| false| | 

## grpc-validation
### grpc.validation
**Class:** `grpcstarter.extensions.validation.GrpcValidationProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| backend| grpcstarter.extensions.validation.GrpcValidationProperties$Backend| Validation implementation.| | | 
| enabled| java.lang.Boolean| Whether to enable validation, default is \{@code true}.| true| | 
### grpc.validation.client
**Class:** `grpcstarter.extensions.validation.GrpcValidationProperties$Client`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable validation, default is \{@code true}.| true| | 
| order| java.lang.Integer| Validating interceptor order, default is \{@code 0}.| 0| | 
### grpc.validation.server
**Class:** `grpcstarter.extensions.validation.GrpcValidationProperties$Server`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable validation, default is \{@code true}.| true| | 
| order| java.lang.Integer| Validating interceptor order, default is \{@code 0}.| 0| | 

