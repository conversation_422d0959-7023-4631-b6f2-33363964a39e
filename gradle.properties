group=io.github.danielliu1123
version=4.0.0-SNAPSHOT

# Spring related
# https://github.com/spring-projects/spring-boot
springBootVersion=4.0.0-M2
# https://docs.spring.io/spring-cloud-release/reference/index.html
# https://central.sonatype.com/artifact/org.springframework.cloud/spring-cloud-dependencies
#springCloudVersion=2025.0.0
springCloudCommonsVersion=4.3.0
# https://github.com/spring-gradle-plugins/dependency-management-plugin
springDependencyManagementVersion=1.1.7

# https://github.com/rodnansol/spring-configuration-property-documenter
springConfigurationPropertyDocumenterVersion=0.7.1

# gRPC related
# https://github.com/google/protobuf-gradle-plugin
protobufGradlePluginVersion=0.9.5
# https://central.sonatype.com/artifact/io.grpc/grpc-protobuf/dependencies
grpcVersion=1.72.0
# https://central.sonatype.com/artifact/com.google.api/api-common/dependencies
googleApiCommonVersion=2.49.0
# https://central.sonatype.com/artifact/io.grpc/grpc-protobuf/dependencies
protobufVersion=3.25.5
# https://central.sonatype.com/artifact/io.grpc/grpc-protobuf/dependencies
protoGoogleCommonProtosVersion=2.51.0
# https://github.com/bufbuild/protoc-gen-validate
# Do NOT upgrade, >= 1.2.0 works with protobuf 4.x
# https://github.com/bufbuild/protoc-gen-validate/blob/v1.1.0/java/pom.xml#L42
pgvVersion=1.1.0
# https://github.com/bufbuild/protovalidate-java
# Do NOT upgrade, until protobuf for grpc upgrade to protobuf 4.x, see https://github.com/bufbuild/protovalidate-java/releases/tag/v0.3.0
# NOTE: Protobuf broke backward compatibility in version 4.x, but restored it in versions >= 4.26.x.
# Therefore, protobufVersion values must be >= 4.26.x
protovalidateVersion=0.2.1
# https://github.com/graalvm/native-build-tools
graalvmBuildToolsVersion=0.11.0

# https://github.com/springdoc/springdoc-openapi
springdocVersion=2.8.9
# https://github.com/DanielLiu1123/springdoc-bridge
springdocBridgeVersion=0.3.5

# Code quality
# https://plugins.gradle.org/plugin/com.diffplug.gradle.spotless
spotlessVersion=7.2.1
# https://plugins.gradle.org/plugin/com.github.spotbugs
spotbugsVersion=6.1.13
# https://github.com/spotbugs/spotbugs-gradle-plugin/blob/master/build.gradle.kts
spotbugsAnnotationsVersion=4.8.6

# Publishing
# https://github.com/jreleaser/jreleaser
jReleaserVersion=1.19.0

# Others
javaxValidationApiVersion=1.3.2
# https://github.com/DanielLiu1123/classpath-replacer
classpathReplacerVersion=3.2.0

org.gradle.jvmargs=-Xmx4g
org.gradle.parallel=true
org.gradle.caching=true
